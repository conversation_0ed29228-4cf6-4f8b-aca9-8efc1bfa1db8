@extends('layouts.app')

@section('title', 'Apply for ' . $job->title)
@section('meta_description', 'Apply for the ' . $job->title . ' position at our company. Join our team and help us build amazing products.')

@section('content')
<!-- Job Header -->
<section class="bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 text-white py-12">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
            <div class="flex items-center mb-4">
                <a href="{{ route('careers.show', $job) }}" class="text-blue-200 hover:text-white mr-4">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </a>
                <div>
                    <h1 class="text-2xl lg:text-3xl font-bold">Apply for {{ $job->title }}</h1>
                    <p class="text-blue-100 mt-1">{{ $job->department }} • {{ $job->location }}</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Application Form -->
<section class="py-12">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
            <div class="bg-white rounded-lg shadow-lg">
                <!-- Progress Steps -->
                <div class="border-b border-gray-200 px-6 py-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">1</div>
                                <span class="ml-2 text-sm font-medium text-gray-900">Personal Info</span>
                            </div>
                            <div class="w-8 border-t border-gray-300"></div>
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center text-sm font-medium">2</div>
                                <span class="ml-2 text-sm font-medium text-gray-500">Professional</span>
                            </div>
                            <div class="w-8 border-t border-gray-300"></div>
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center text-sm font-medium">3</div>
                                <span class="ml-2 text-sm font-medium text-gray-500">Documents</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Content -->
                <form method="POST" action="{{ route('careers.store-application', $job) }}" enctype="multipart/form-data" class="p-6">
                    @csrf

                    <!-- Step 1: Personal Information -->
                    <div id="step-1" class="step-content">
                        <h2 class="text-xl font-semibold text-gray-900 mb-6">Personal Information</h2>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- First Name -->
                            <div>
                                <label for="first_name" class="block text-sm font-medium text-gray-700 mb-1">First Name *</label>
                                <input type="text" name="first_name" id="first_name" required
                                       value="{{ old('first_name', $contactInfo['first_name'] ?? '') }}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('first_name') border-red-500 @enderror">
                                @error('first_name')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Last Name -->
                            <div>
                                <label for="last_name" class="block text-sm font-medium text-gray-700 mb-1">Last Name *</label>
                                <input type="text" name="last_name" id="last_name" required
                                       value="{{ old('last_name', $contactInfo['last_name'] ?? '') }}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('last_name') border-red-500 @enderror">
                                @error('last_name')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Email -->
                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email Address *</label>
                                <input type="email" name="email" id="email" required
                                       value="{{ old('email', $contactInfo['email'] ?? '') }}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('email') border-red-500 @enderror">
                                @error('email')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Phone -->
                            <div>
                                <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">Phone Number *</label>
                                <input type="tel" name="phone" id="phone" required
                                       value="{{ old('phone', $contactInfo['phone'] ?? '') }}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('phone') border-red-500 @enderror">
                                @error('phone')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Date of Birth -->
                            <div>
                                <label for="date_of_birth" class="block text-sm font-medium text-gray-700 mb-1">Date of Birth</label>
                                <input type="date" name="date_of_birth" id="date_of_birth"
                                       value="{{ old('date_of_birth') }}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('date_of_birth') border-red-500 @enderror">
                                @error('date_of_birth')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Nationality -->
                            <div>
                                <label for="nationality" class="block text-sm font-medium text-gray-700 mb-1">Nationality</label>
                                <input type="text" name="nationality" id="nationality"
                                       value="{{ old('nationality') }}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('nationality') border-red-500 @enderror">
                                @error('nationality')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Address -->
                        <div class="mt-6">
                            <label for="address" class="block text-sm font-medium text-gray-700 mb-1">Address</label>
                            <textarea name="address" id="address" rows="3"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('address') border-red-500 @enderror">{{ old('address') }}</textarea>
                            @error('address')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
                            <!-- City -->
                            <div>
                                <label for="city" class="block text-sm font-medium text-gray-700 mb-1">City</label>
                                <input type="text" name="city" id="city"
                                       value="{{ old('city') }}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('city') border-red-500 @enderror">
                                @error('city')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Province -->
                            <div>
                                <label for="province" class="block text-sm font-medium text-gray-700 mb-1">Province</label>
                                <input type="text" name="province" id="province"
                                       value="{{ old('province') }}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('province') border-red-500 @enderror">
                                @error('province')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Country -->
                            <div>
                                <label for="country" class="block text-sm font-medium text-gray-700 mb-1">Country *</label>
                                <input type="text" name="country" id="country" required
                                       value="{{ old('country', $contactInfo['country'] ?? 'South Africa') }}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('country') border-red-500 @enderror">
                                @error('country')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Navigation -->
                        <div class="flex justify-end mt-8">
                            <button type="button" onclick="nextStep()" class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors">
                                Next: Professional Info
                            </button>
                        </div>
                    </div>

                    <!-- Step 2: Professional Information -->
                    <div id="step-2" class="step-content hidden">
                        <h2 class="text-xl font-semibold text-gray-900 mb-6">Professional Information</h2>

                        <!-- Cover Letter -->
                        <div class="mb-6">
                            <label for="cover_letter" class="block text-sm font-medium text-gray-700 mb-1">Cover Letter *</label>
                            <textarea name="cover_letter" id="cover_letter" rows="6" required
                                      placeholder="Tell us why you're interested in this position and what makes you a great fit..."
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('cover_letter') border-red-500 @enderror">{{ old('cover_letter') }}</textarea>
                            @error('cover_letter')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Experience Summary -->
                        <div class="mb-6">
                            <label for="experience_summary" class="block text-sm font-medium text-gray-700 mb-1">Experience Summary *</label>
                            <textarea name="experience_summary" id="experience_summary" rows="4" required
                                      placeholder="Briefly describe your relevant work experience..."
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('experience_summary') border-red-500 @enderror">{{ old('experience_summary') }}</textarea>
                            @error('experience_summary')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Current Position -->
                            <div>
                                <label for="current_position" class="block text-sm font-medium text-gray-700 mb-1">Current Position</label>
                                <input type="text" name="current_position" id="current_position"
                                       value="{{ old('current_position') }}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('current_position') border-red-500 @enderror">
                                @error('current_position')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Current Company -->
                            <div>
                                <label for="current_company" class="block text-sm font-medium text-gray-700 mb-1">Current Company</label>
                                <input type="text" name="current_company" id="current_company"
                                       value="{{ old('current_company') }}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('current_company') border-red-500 @enderror">
                                @error('current_company')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Expected Salary -->
                            <div>
                                <label for="expected_salary" class="block text-sm font-medium text-gray-700 mb-1">Expected Salary (ZAR/month)</label>
                                <input type="number" name="expected_salary" id="expected_salary" min="0"
                                       value="{{ old('expected_salary') }}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('expected_salary') border-red-500 @enderror">
                                @error('expected_salary')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Available Start Date -->
                            <div>
                                <label for="available_start_date" class="block text-sm font-medium text-gray-700 mb-1">Available Start Date</label>
                                <input type="date" name="available_start_date" id="available_start_date"
                                       value="{{ old('available_start_date') }}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('available_start_date') border-red-500 @enderror">
                                @error('available_start_date')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Education -->
                        <div class="mt-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Education</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Highest Qualification -->
                                <div>
                                    <label for="highest_qualification" class="block text-sm font-medium text-gray-700 mb-1">Highest Qualification *</label>
                                    <input type="text" name="highest_qualification" id="highest_qualification" required
                                           value="{{ old('highest_qualification') }}"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('highest_qualification') border-red-500 @enderror">
                                    @error('highest_qualification')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Institution -->
                                <div>
                                    <label for="institution" class="block text-sm font-medium text-gray-700 mb-1">Institution</label>
                                    <input type="text" name="institution" id="institution"
                                           value="{{ old('institution') }}"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('institution') border-red-500 @enderror">
                                    @error('institution')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Navigation -->
                        <div class="flex justify-between mt-8">
                            <button type="button" onclick="prevStep()" class="bg-gray-300 text-gray-700 px-6 py-2 rounded-md hover:bg-gray-400 transition-colors">
                                Previous
                            </button>
                            <button type="button" onclick="nextStep()" class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors">
                                Next: Documents
                            </button>
                        </div>
                    </div>

                    <!-- Step 3: Documents & Final -->
                    <div id="step-3" class="step-content hidden">
                        <h2 class="text-xl font-semibold text-gray-900 mb-6">Documents & Additional Information</h2>

                        <!-- File Attachments -->
                        <div class="mb-6">
                            <label for="attachments" class="block text-sm font-medium text-gray-700 mb-1">Attachments (CV, Portfolio, Certificates)</label>
                            <input type="file" name="attachments[]" id="attachments" multiple
                                   accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('attachments') border-red-500 @enderror">
                            <p class="mt-1 text-sm text-gray-500">Accepted formats: PDF, DOC, DOCX, JPG, PNG. Max 25MB per file.</p>
                            @error('attachments')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Additional Notes -->
                        <div class="mb-6">
                            <label for="additional_notes" class="block text-sm font-medium text-gray-700 mb-1">Additional Notes</label>
                            <textarea name="additional_notes" id="additional_notes" rows="4"
                                      placeholder="Any additional information you'd like to share..."
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 @error('additional_notes') border-red-500 @enderror">{{ old('additional_notes') }}</textarea>
                            @error('additional_notes')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Preferences -->
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Preferences</h3>
                            <div class="space-y-3">
                                <label class="flex items-center">
                                    <input type="checkbox" name="willing_to_relocate" value="1" {{ old('willing_to_relocate') ? 'checked' : '' }}
                                           class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">Willing to relocate</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" name="willing_to_travel" value="1" {{ old('willing_to_travel') ? 'checked' : '' }}
                                           class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">Willing to travel</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" name="has_drivers_license" value="1" {{ old('has_drivers_license') ? 'checked' : '' }}
                                           class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">Have driver's license</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" name="newsletter_signup" value="1" {{ old('newsletter_signup') ? 'checked' : '' }}
                                           class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">Subscribe to our newsletter</span>
                                </label>
                            </div>
                        </div>

                        <!-- Terms -->
                        <div class="mb-6">
                            <label class="flex items-start">
                                <input type="checkbox" name="terms_accepted" value="1" required {{ old('terms_accepted') ? 'checked' : '' }}
                                       class="mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">
                                    I agree to the <a href="{{ route('terms') }}" target="_blank" class="text-blue-600 hover:underline">Terms of Service</a>
                                    and <a href="{{ route('privacy') }}" target="_blank" class="text-blue-600 hover:underline">Privacy Policy</a> *
                                </span>
                            </label>
                            @error('terms_accepted')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Navigation -->
                        <div class="flex justify-between mt-8">
                            <button type="button" onclick="prevStep()" class="bg-gray-300 text-gray-700 px-6 py-2 rounded-md hover:bg-gray-400 transition-colors">
                                Previous
                            </button>
                            <button type="submit" class="bg-green-600 text-white px-8 py-2 rounded-md hover:bg-green-700 transition-colors">
                                Submit Application
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>

@push('scripts')
<script>
let currentStep = 1;
const totalSteps = 3;

function showStep(step) {
    // Hide all steps
    for (let i = 1; i <= totalSteps; i++) {
        document.getElementById(`step-${i}`).classList.add('hidden');
    }

    // Show current step
    document.getElementById(`step-${step}`).classList.remove('hidden');

    // Update progress indicators
    updateProgress(step);
}

function updateProgress(step) {
    for (let i = 1; i <= totalSteps; i++) {
        const circle = document.querySelector(`.step-content:nth-child(${i}) .w-8.h-8`);
        const text = document.querySelector(`.step-content:nth-child(${i}) .text-sm`);

        if (i <= step) {
            // Completed or current step
            circle.classList.remove('bg-gray-300', 'text-gray-600');
            circle.classList.add('bg-blue-600', 'text-white');
            text.classList.remove('text-gray-500');
            text.classList.add('text-gray-900');
        } else {
            // Future step
            circle.classList.remove('bg-blue-600', 'text-white');
            circle.classList.add('bg-gray-300', 'text-gray-600');
            text.classList.remove('text-gray-900');
            text.classList.add('text-gray-500');
        }
    }
}

function nextStep() {
    if (currentStep < totalSteps) {
        currentStep++;
        showStep(currentStep);
    }
}

function prevStep() {
    if (currentStep > 1) {
        currentStep--;
        showStep(currentStep);
    }
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    showStep(1);
});
</script>
@endpush
@endsection