<?php

namespace App\Services;

use App\Models\ActivityLog;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Jenssegers\Agent\Agent;

class ActivityLogger
{
    protected Agent $agent;
    protected Request $request;

    public function __construct()
    {
        $this->agent = new Agent();
        $this->request = request();
    }

    /**
     * Log password reset request activity.
     */
    public function logPasswordResetRequest(
        string $email,
        bool $emailExists,
        bool $wasRateLimited = false,
        ?string $failureReason = null
    ): ActivityLog {
        $user = User::where('email', $email)->first();

        // Always show success to user for security, but log the real situation for admin
        $publicStatus = 'success';
        $adminDescription = $this->getAdminDescription($email, $emailExists, $wasRateLimited);
        $riskScore = $this->calculateRiskScore($email, $emailExists);

        // Increase risk score for non-existent emails (potential enumeration attempt)
        if (!$emailExists) {
            $riskScore += 25; // Higher risk for email enumeration attempts
        }

        if ($wasRateLimited) {
            $failureReason = 'Rate limited - too many requests';
            $riskScore += 20; // Additional risk for rate limiting
        }

        // Flag as suspicious if high risk score or multiple non-existent email attempts
        $isSuspicious = $riskScore >= 50 || $this->isRepeatedEnumerationAttempt($email);

        return $this->createActivityLog([
            'user_id' => $user?->id,
            'user_email' => $email,
            'user_name' => $user ? "{$user->first_name} {$user->last_name}" : null,
            'activity_type' => 'password_reset_request',
            'activity_description' => $adminDescription,
            'status' => $publicStatus, // Always success for user
            'failure_reason' => $failureReason,
            'risk_score' => $riskScore,
            'is_suspicious' => $isSuspicious,
            'security_notes' => $this->getSecurityNotes($email, $emailExists, $wasRateLimited),
            'request_data' => [
                'email_submitted' => $email,
                'email_exists_in_system' => $emailExists,
                'user_account_found' => $user ? true : false,
                'user_account_active' => $user?->is_active ?? false,
                'was_rate_limited' => $wasRateLimited,
                'enumeration_attempt' => !$emailExists,
                'form_data' => $this->sanitizeRequestData($this->request->all()),
                'admin_notes' => $emailExists
                    ? 'Valid email - reset link sent to existing user'
                    : 'ENUMERATION ATTEMPT - Email does not exist in system',
            ],
            'response_data' => [
                'public_response' => 'success', // What user sees
                'public_message' => 'If an account with that email address exists, you will receive a password reset link shortly.',
                'actual_email_exists' => $emailExists,
                'actual_email_sent' => $emailExists && !$wasRateLimited,
                'admin_action_taken' => $emailExists && !$wasRateLimited ? 'Email sent' : 'No email sent',
                'security_status' => $emailExists ? 'legitimate_request' : 'potential_enumeration',
            ],
        ]);
    }

    /**
     * Log successful password reset.
     */
    public function logPasswordResetSuccess(User $user, string $token): ActivityLog
    {
        return $this->createActivityLog([
            'user_id' => $user->id,
            'user_email' => $user->email,
            'user_name' => "{$user->first_name} {$user->last_name}",
            'activity_type' => 'password_reset_success',
            'activity_description' => 'Password successfully reset using valid token',
            'status' => 'success',
            'risk_score' => 10, // Low risk for successful reset
            'request_data' => [
                'token_used' => substr($token, 0, 8) . '...', // Partial token for reference
                'form_data' => $this->sanitizeRequestData($this->request->all()),
            ],
            'response_data' => [
                'password_changed' => true,
                'sessions_cleared' => true,
                'remember_tokens_cleared' => true,
            ],
        ]);
    }

    /**
     * Log failed password reset attempt.
     */
    public function logPasswordResetFailed(
        string $email,
        string $reason,
        ?string $token = null
    ): ActivityLog {
        $user = User::where('email', $email)->first();
        $riskScore = $this->calculateFailureRiskScore($reason);

        return $this->createActivityLog([
            'user_id' => $user?->id,
            'user_email' => $email,
            'user_name' => $user ? "{$user->first_name} {$user->last_name}" : null,
            'activity_type' => 'password_reset_failed',
            'activity_description' => "Password reset failed: {$reason}",
            'status' => 'failed',
            'failure_reason' => $reason,
            'risk_score' => $riskScore,
            'is_suspicious' => $riskScore >= 60,
            'request_data' => [
                'email' => $email,
                'token_provided' => $token ? (substr($token, 0, 8) . '...') : null,
                'failure_type' => $reason,
                'form_data' => $this->sanitizeRequestData($this->request->all()),
            ],
        ]);
    }

    /**
     * Create activity log with common data.
     */
    protected function createActivityLog(array $data): ActivityLog
    {
        $deviceInfo = $this->getDeviceInfo();
        $locationInfo = $this->getLocationInfo();

        $commonData = [
            'uuid' => Str::uuid(),
            'ip_address' => $this->request->ip(),
            'user_agent' => $this->request->userAgent(),
            'device_type' => $deviceInfo['type'],
            'browser' => $deviceInfo['browser'],
            'platform' => $deviceInfo['platform'],
            'country' => $locationInfo['country'],
            'region' => $locationInfo['region'],
            'city' => $locationInfo['city'],
            'latitude' => $locationInfo['latitude'],
            'longitude' => $locationInfo['longitude'],
            'url' => $this->request->fullUrl(),
            'method' => $this->request->method(),
            'session_id' => session()->getId(),
            'request_id' => $this->request->header('X-Request-ID') ?? Str::uuid(),
            'occurred_at' => now(),
        ];

        return ActivityLog::create(array_merge($commonData, $data));
    }

    /**
     * Get device information from user agent.
     */
    protected function getDeviceInfo(): array
    {
        return [
            'type' => $this->agent->deviceType(),
            'browser' => $this->agent->browser() . ' ' . $this->agent->version($this->agent->browser()),
            'platform' => $this->agent->platform() . ' ' . $this->agent->version($this->agent->platform()),
        ];
    }

    /**
     * Get location information from IP address.
     */
    protected function getLocationInfo(): array
    {
        $ip = $this->request->ip();
        
        // Skip location lookup for local IPs
        if ($this->isLocalIp($ip)) {
            return [
                'country' => 'Local',
                'region' => 'Local',
                'city' => 'Local',
                'latitude' => null,
                'longitude' => null,
            ];
        }

        try {
            // Using a free IP geolocation service
            $response = Http::timeout(5)->get("http://ip-api.com/json/{$ip}");
            
            if ($response->successful()) {
                $data = $response->json();
                
                return [
                    'country' => $data['country'] ?? null,
                    'region' => $data['regionName'] ?? null,
                    'city' => $data['city'] ?? null,
                    'latitude' => $data['lat'] ?? null,
                    'longitude' => $data['lon'] ?? null,
                ];
            }
        } catch (\Exception $e) {
            // Log the error but don't fail the activity logging
            \Log::warning('Failed to get location info', [
                'ip' => $ip,
                'error' => $e->getMessage()
            ]);
        }

        return [
            'country' => null,
            'region' => null,
            'city' => null,
            'latitude' => null,
            'longitude' => null,
        ];
    }

    /**
     * Check if IP address is local.
     */
    protected function isLocalIp(string $ip): bool
    {
        return in_array($ip, ['127.0.0.1', '::1', 'localhost']) || 
               filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) === false;
    }

    /**
     * Calculate risk score for password reset request.
     */
    protected function calculateRiskScore(string $email, bool $emailExists): int
    {
        $score = 0;
        
        // Base score for non-existent email
        if (!$emailExists) {
            $score += 30;
        }
        
        // Check for recent failed attempts from same IP
        $recentFailures = ActivityLog::byIpAddress($this->request->ip())
            ->where('activity_type', 'password_reset_request')
            ->where('status', 'failed')
            ->recent(1)
            ->count();
        
        $score += min($recentFailures * 10, 40);
        
        // Check for suspicious user agent patterns
        $userAgent = $this->request->userAgent();
        if (empty($userAgent) || str_contains(strtolower($userAgent), 'bot')) {
            $score += 25;
        }
        
        return min($score, 100);
    }

    /**
     * Calculate risk score for failed password reset.
     */
    protected function calculateFailureRiskScore(string $reason): int
    {
        return match ($reason) {
            'Invalid token' => 40,
            'Expired token' => 20,
            'Token already used' => 30,
            'Invalid email' => 50,
            'Validation failed' => 25,
            default => 35,
        };
    }

    /**
     * Get admin-friendly description of the password reset attempt.
     */
    protected function getAdminDescription(string $email, bool $emailExists, bool $wasRateLimited): string
    {
        if ($wasRateLimited) {
            return "Password reset request RATE LIMITED for: {$email}";
        }

        if ($emailExists) {
            return "Password reset requested for EXISTING user: {$email}";
        }

        return "Password reset requested for NON-EXISTENT email: {$email} (ENUMERATION ATTEMPT)";
    }

    /**
     * Get security notes for admin review.
     */
    protected function getSecurityNotes(string $email, bool $emailExists, bool $wasRateLimited): ?string
    {
        $notes = [];

        if (!$emailExists) {
            $notes[] = "⚠️ EMAIL ENUMERATION: User attempted to reset password for non-existent email";
        }

        if ($wasRateLimited) {
            $notes[] = "🚫 RATE LIMITED: Too many attempts from this IP/session";
        }

        if ($this->isRepeatedEnumerationAttempt($email)) {
            $notes[] = "🔍 REPEATED ENUMERATION: Multiple non-existent email attempts detected";
        }

        if ($this->isSuspiciousEmailPattern($email)) {
            $notes[] = "🤖 SUSPICIOUS PATTERN: Email follows common enumeration patterns";
        }

        return empty($notes) ? null : implode(' | ', $notes);
    }

    /**
     * Check if this is a repeated enumeration attempt from the same source.
     */
    protected function isRepeatedEnumerationAttempt(string $email): bool
    {
        $recentEnumerationAttempts = ActivityLog::byIpAddress($this->request->ip())
            ->where('activity_type', 'password_reset_request')
            ->where('request_data->enumeration_attempt', true)
            ->recent(1) // Last hour
            ->count();

        return $recentEnumerationAttempts >= 2;
    }

    /**
     * Check if email follows suspicious enumeration patterns.
     */
    protected function isSuspiciousEmailPattern(string $email): bool
    {
        $suspiciousPatterns = [
            '/^admin@/',
            '/^test@/',
            '/^user\d+@/',
            '/^[a-z]{1,3}@/',
            '/^\d+@/',
            '/@test\./',
            '/@example\./',
            '/@gmail\.com$/',
        ];

        foreach ($suspiciousPatterns as $pattern) {
            if (preg_match($pattern, strtolower($email))) {
                return true;
            }
        }

        return false;
    }

    /**
     * Log generic customer dashboard activity.
     */
    public function logCustomerActivity(
        string $activityType,
        string $description,
        string $status = 'success',
        ?string $failureReason = null,
        array $requestData = [],
        array $responseData = [],
        ?int $riskScore = null
    ): ActivityLog {
        $user = auth()->user();

        // Calculate risk score if not provided
        if ($riskScore === null) {
            $riskScore = $this->calculateGenericRiskScore($activityType, $status);
        }

        return $this->createActivityLog([
            'user_id' => $user?->id,
            'user_email' => $user?->email,
            'user_name' => $user ? "{$user->first_name} {$user->last_name}" : null,
            'activity_type' => $activityType,
            'activity_description' => $description,
            'status' => $status,
            'failure_reason' => $failureReason,
            'risk_score' => $riskScore,
            'is_suspicious' => $riskScore >= 60,
            'request_data' => array_merge([
                'form_data' => $this->sanitizeRequestData($this->request->all()),
                'route_name' => $this->request->route()?->getName(),
                'route_parameters' => $this->request->route()?->parameters() ?? [],
            ], $requestData),
            'response_data' => $responseData,
        ]);
    }

    /**
     * Log profile update activity.
     */
    public function logProfileUpdate(
        array $changes,
        bool $success = true,
        ?string $failureReason = null
    ): ActivityLog {
        $user = auth()->user();
        $changeTypes = array_keys($changes);

        $description = $success
            ? "Profile updated: " . implode(', ', $changeTypes)
            : "Profile update failed: " . ($failureReason ?? 'Unknown error');

        return $this->logCustomerActivity(
            'profile_update',
            $description,
            $success ? 'success' : 'failed',
            $failureReason,
            [
                'fields_changed' => $changeTypes,
                'changes_made' => $this->sanitizeProfileChanges($changes),
                'user_role' => $user?->role?->name,
            ],
            [
                'profile_updated' => $success,
                'fields_affected' => count($changeTypes),
            ],
            $success ? 5 : 25
        );
    }

    /**
     * Log project application activity.
     */
    public function logProjectApplicationActivity(
        string $action,
        ?int $applicationId = null,
        array $applicationData = [],
        bool $success = true,
        ?string $failureReason = null
    ): ActivityLog {
        $user = auth()->user();

        $descriptions = [
            'create' => $success ? 'Project application submitted' : 'Project application submission failed',
            'update' => $success ? 'Project application updated' : 'Project application update failed',
            'delete' => $success ? 'Project application deleted' : 'Project application deletion failed',
            'view' => 'Project application viewed',
        ];

        $description = $descriptions[$action] ?? "Project application {$action}";
        if ($applicationId) {
            $description .= " (ID: {$applicationId})";
        }

        return $this->logCustomerActivity(
            "project_application_{$action}",
            $description,
            $success ? 'success' : 'failed',
            $failureReason,
            [
                'application_id' => $applicationId,
                'action_performed' => $action,
                'application_data' => $this->sanitizeApplicationData($applicationData),
                'user_role' => $user?->role?->name,
            ],
            [
                'action_completed' => $success,
                'application_affected' => $applicationId,
            ],
            $this->calculateApplicationRiskScore($action, $success)
        );
    }

    /**
     * Log file upload activity.
     */
    public function logFileUpload(
        string $fileName,
        string $fileType,
        int $fileSize,
        string $uploadContext,
        bool $success = true,
        ?string $failureReason = null,
        array $securityScanResults = []
    ): ActivityLog {
        $user = auth()->user();

        $description = $success
            ? "File uploaded: {$fileName} ({$fileType}, " . $this->formatFileSize($fileSize) . ")"
            : "File upload failed: {$fileName} - " . ($failureReason ?? 'Unknown error');

        return $this->logCustomerActivity(
            'file_upload',
            $description,
            $success ? 'success' : 'failed',
            $failureReason,
            [
                'file_name' => $fileName,
                'file_type' => $fileType,
                'file_size' => $fileSize,
                'file_size_formatted' => $this->formatFileSize($fileSize),
                'upload_context' => $uploadContext,
                'security_scan_results' => $securityScanResults,
                'user_role' => $user?->role?->name,
            ],
            [
                'upload_completed' => $success,
                'file_processed' => $success,
                'security_passed' => empty($securityScanResults['threats_found'] ?? []),
            ],
            $this->calculateFileUploadRiskScore($fileType, $fileSize, $securityScanResults, $success)
        );
    }

    /**
     * Log order activity.
     */
    public function logOrderActivity(
        string $action,
        ?int $orderId = null,
        array $orderData = [],
        bool $success = true,
        ?string $failureReason = null
    ): ActivityLog {
        $user = auth()->user();

        $descriptions = [
            'create' => $success ? 'Order placed' : 'Order placement failed',
            'update' => $success ? 'Order updated' : 'Order update failed',
            'cancel' => $success ? 'Order cancelled' : 'Order cancellation failed',
            'view' => 'Order viewed',
        ];

        $description = $descriptions[$action] ?? "Order {$action}";
        if ($orderId) {
            $description .= " (Order #: {$orderId})";
        }

        return $this->logCustomerActivity(
            "order_{$action}",
            $description,
            $success ? 'success' : 'failed',
            $failureReason,
            [
                'order_id' => $orderId,
                'action_performed' => $action,
                'order_data' => $this->sanitizeOrderData($orderData),
                'user_role' => $user?->role?->name,
            ],
            [
                'action_completed' => $success,
                'order_affected' => $orderId,
            ],
            $this->calculateOrderRiskScore($action, $success)
        );
    }

    /**
     * Log dashboard access.
     */
    public function logDashboardAccess(
        string $section = 'main',
        array $additionalData = []
    ): ActivityLog {
        $user = auth()->user();

        return $this->logCustomerActivity(
            'dashboard_access',
            "Dashboard accessed: {$section}",
            'success',
            null,
            array_merge([
                'dashboard_section' => $section,
                'user_role' => $user?->role?->name,
                'session_duration' => session()->get('login_time') ? now()->diffInMinutes(session()->get('login_time')) : null,
            ], $additionalData),
            [
                'access_granted' => true,
                'section_loaded' => $section,
            ],
            2 // Very low risk for dashboard access
        );
    }

    /**
     * Calculate generic risk score based on activity type and status.
     */
    protected function calculateGenericRiskScore(string $activityType, string $status): int
    {
        $baseScores = [
            'profile_update' => 10,
            'project_application_create' => 5,
            'project_application_update' => 5,
            'project_application_delete' => 15,
            'file_upload' => 20,
            'order_create' => 10,
            'order_update' => 10,
            'order_cancel' => 15,
            'dashboard_access' => 2,
        ];

        $baseScore = $baseScores[$activityType] ?? 10;

        // Increase score for failed activities
        if ($status === 'failed') {
            $baseScore += 20;
        }

        return min($baseScore, 100);
    }

    /**
     * Calculate risk score for project applications.
     */
    protected function calculateApplicationRiskScore(string $action, bool $success): int
    {
        $scores = [
            'create' => $success ? 5 : 25,
            'update' => $success ? 5 : 20,
            'delete' => $success ? 15 : 30,
            'view' => 2,
        ];

        return $scores[$action] ?? 10;
    }

    /**
     * Calculate risk score for file uploads.
     */
    protected function calculateFileUploadRiskScore(
        string $fileType,
        int $fileSize,
        array $securityScanResults,
        bool $success
    ): int {
        $score = $success ? 10 : 40;

        // Increase score for executable file types
        $dangerousTypes = ['exe', 'bat', 'cmd', 'scr', 'pif', 'com'];
        $extension = strtolower(pathinfo($fileType, PATHINFO_EXTENSION));
        if (in_array($extension, $dangerousTypes)) {
            $score += 50;
        }

        // Increase score for large files
        if ($fileSize > 50 * 1024 * 1024) { // 50MB
            $score += 15;
        }

        // Increase score if security threats found
        if (!empty($securityScanResults['threats_found'] ?? [])) {
            $score += 60;
        }

        return min($score, 100);
    }

    /**
     * Calculate risk score for orders.
     */
    protected function calculateOrderRiskScore(string $action, bool $success): int
    {
        $scores = [
            'create' => $success ? 10 : 30,
            'update' => $success ? 8 : 25,
            'cancel' => $success ? 15 : 25,
            'view' => 2,
        ];

        return $scores[$action] ?? 10;
    }

    /**
     * Format file size for display.
     */
    protected function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * Sanitize profile changes for logging.
     */
    protected function sanitizeProfileChanges(array $changes): array
    {
        $sensitive = ['password', 'password_confirmation'];

        foreach ($sensitive as $field) {
            if (isset($changes[$field])) {
                $changes[$field] = '***REDACTED***';
            }
        }

        return $changes;
    }

    /**
     * Sanitize application data for logging.
     */
    protected function sanitizeApplicationData(array $data): array
    {
        // Remove sensitive file data but keep metadata
        if (isset($data['attachments'])) {
            $data['attachments'] = array_map(function ($attachment) {
                return [
                    'file_name' => $attachment['file_name'] ?? 'unknown',
                    'file_size' => $attachment['file_size'] ?? 0,
                    'file_type' => $attachment['file_type'] ?? 'unknown',
                ];
            }, $data['attachments']);
        }

        return $this->sanitizeRequestData($data);
    }

    /**
     * Sanitize order data for logging.
     */
    protected function sanitizeOrderData(array $data): array
    {
        $sensitive = ['payment_method', 'card_number', 'cvv'];

        foreach ($sensitive as $field) {
            if (isset($data[$field])) {
                $data[$field] = '***REDACTED***';
            }
        }

        return $data;
    }

    /**
     * Sanitize request data for logging.
     */
    protected function sanitizeRequestData(array $data): array
    {
        $sensitive = ['password', 'password_confirmation', 'token', '_token', 'card_number', 'cvv', 'ssn'];

        foreach ($sensitive as $field) {
            if (isset($data[$field])) {
                $data[$field] = '***REDACTED***';
            }
        }

        return $data;
    }
}
