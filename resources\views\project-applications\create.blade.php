@extends('layouts.app')

@section('title', 'Submit Project Application - ' . __('common.company_name'))

@section('content')
<div class="min-h-screen bg-gray-50 py-12">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-12">
            <h1 class="text-3xl font-bold text-gray-900 mb-4">Submit Project Application</h1>
            <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                Tell us about your project requirements and we'll get back to you with a detailed proposal.
            </p>
        </div>

        <!-- Application Form -->
        <div class="bg-white shadow-lg rounded-lg overflow-hidden">
            <div class="px-6 py-8">
                <form action="{{ route('project-applications.store') }}" method="POST" enctype="multipart/form-data" class="space-y-8">
                    @csrf

                    <!-- Service Selection -->
                    <div class="floating-input-group">
                        <select id="service_id" name="service_id" class="floating-input peer">
                            <option value=""></option>
                            @foreach($services as $service)
                                <option value="{{ $service->id }}" {{ old('service_id') == $service->id ? 'selected' : '' }}>
                                    {{ $service->name }}
                                </option>
                            @endforeach
                        </select>
                        <label for="service_id" class="floating-label">Service Type</label>
                        @error('service_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Project Title -->
                    <div class="floating-input-group">
                        <input type="text" id="title" name="title" value="{{ old('title') }}" required 
                               class="floating-input peer" placeholder=" ">
                        <label for="title" class="floating-label">Project Title *</label>
                        @error('title')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Project Description -->
                    <div class="floating-input-group">
                        <textarea id="description" name="description" rows="6" required 
                                  class="floating-input peer resize-vertical" placeholder=" ">{{ old('description') }}</textarea>
                        <label for="description" class="floating-label">Project Description *</label>
                        <p class="mt-1 text-sm text-gray-500">Describe your project goals, target audience, and key features</p>
                        @error('description')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Requirements -->
                    <div class="floating-input-group">
                        <textarea id="requirements" name="requirements" rows="4" 
                                  class="floating-input peer resize-vertical" placeholder=" ">{{ old('requirements') }}</textarea>
                        <label for="requirements" class="floating-label">Technical Requirements</label>
                        <p class="mt-1 text-sm text-gray-500">Specific technologies, integrations, or features needed</p>
                        @error('requirements')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Budget and Timeline Row -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="floating-input-group">
                            <select id="budget_range" name="budget_range" class="floating-input peer">
                                <option value=""></option>
                                <option value="under-5k" {{ old('budget_range') == 'under-5k' ? 'selected' : '' }}>Under R5,000</option>
                                <option value="5k-15k" {{ old('budget_range') == '5k-15k' ? 'selected' : '' }}>R5,000 - R15,000</option>
                                <option value="15k-50k" {{ old('budget_range') == '15k-50k' ? 'selected' : '' }}>R15,000 - R50,000</option>
                                <option value="50k-100k" {{ old('budget_range') == '50k-100k' ? 'selected' : '' }}>R50,000 - R100,000</option>
                                <option value="over-100k" {{ old('budget_range') == 'over-100k' ? 'selected' : '' }}>Over R100,000</option>
                                <option value="discuss" {{ old('budget_range') == 'discuss' ? 'selected' : '' }}>Let's Discuss</option>
                            </select>
                            <label for="budget_range" class="floating-label">Budget Range</label>
                            @error('budget_range')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="floating-input-group">
                            <select id="timeline" name="timeline" class="floating-input peer">
                                <option value=""></option>
                                <option value="asap" {{ old('timeline') == 'asap' ? 'selected' : '' }}>ASAP</option>
                                <option value="1-month" {{ old('timeline') == '1-month' ? 'selected' : '' }}>Within 1 Month</option>
                                <option value="2-3-months" {{ old('timeline') == '2-3-months' ? 'selected' : '' }}>2-3 Months</option>
                                <option value="3-6-months" {{ old('timeline') == '3-6-months' ? 'selected' : '' }}>3-6 Months</option>
                                <option value="6-months-plus" {{ old('timeline') == '6-months-plus' ? 'selected' : '' }}>6+ Months</option>
                                <option value="flexible" {{ old('timeline') == 'flexible' ? 'selected' : '' }}>Flexible</option>
                            </select>
                            <label for="timeline" class="floating-label">Timeline</label>
                            @error('timeline')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Priority -->
                    <div class="floating-input-group">
                        <select id="priority" name="priority" required class="floating-input peer">
                            <option value=""></option>
                            <option value="low" {{ old('priority') == 'low' ? 'selected' : '' }}>Low</option>
                            <option value="medium" {{ old('priority', 'medium') == 'medium' ? 'selected' : '' }}>Medium</option>
                            <option value="high" {{ old('priority') == 'high' ? 'selected' : '' }}>High</option>
                            <option value="urgent" {{ old('priority') == 'urgent' ? 'selected' : '' }}>Urgent</option>
                        </select>
                        <label for="priority" class="floating-label">Priority *</label>
                        @error('priority')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- File Upload Section -->
                    <div class="space-y-4">
                        <div class="flex items-center space-x-2">
                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                            </svg>
                            <h3 class="text-lg font-semibold text-gray-900">Project Attachments</h3>
                            <span class="text-sm text-gray-500">(Optional)</span>
                        </div>
                        
                        <div class="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-6 hover:border-blue-400 transition-colors duration-200">
                            <div class="text-center">
                                <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                </svg>
                                <div class="mt-4">
                                    <label for="attachments" class="cursor-pointer">
                                        <span class="mt-2 block text-sm font-medium text-gray-900">
                                            Upload project files, wireframes, designs, or documents
                                        </span>
                                        <span class="mt-1 block text-xs text-gray-500">
                                            PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT, CSV, ZIP, JPG, PNG, WebP up to 25MB each
                                        </span>
                                    </label>
                                    <input id="attachments" name="attachments[]" type="file" class="sr-only" multiple 
                                           accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.csv,.zip,.jpg,.jpeg,.png,.webp">
                                </div>
                                <p class="text-xs text-gray-500 mt-2">
                                    All files are scanned for security before processing
                                </p>
                            </div>
                        </div>
                        
                        <!-- File Preview Area -->
                        <div id="file-preview" class="hidden space-y-2">
                            <h4 class="text-sm font-medium text-gray-900">Selected Files:</h4>
                            <div id="file-list" class="space-y-2"></div>
                        </div>
                        
                        @error('attachments')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        @error('attachments.*')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Submit Button -->
                    <div class="flex justify-end space-x-4">
                        <a href="{{ route('project-applications.index') }}" 
                           class="btn-outline border-gray-300 text-gray-700 hover:bg-gray-50">
                            Cancel
                        </a>
                        <button type="submit" class="btn-primary">
                            Submit Application
                            <svg class="ml-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // File Upload Handling (reuse the same logic from contact form)
    const fileInput = document.getElementById('attachments');
    const filePreview = document.getElementById('file-preview');
    const fileList = document.getElementById('file-list');
    const maxFileSize = 25 * 1024 * 1024; // 25MB
    const allowedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'text/plain',
        'text/csv',
        'application/zip',
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/webp'
    ];

    if (fileInput) {
        fileInput.addEventListener('change', function(e) {
            const files = Array.from(e.target.files);
            fileList.innerHTML = '';
            
            if (files.length === 0) {
                filePreview.classList.add('hidden');
                return;
            }

            files.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'flex items-center justify-between p-3 bg-white border border-gray-200 rounded-lg';
                
                // Validate file
                let isValid = true;
                let errorMessage = '';
                
                if (file.size > maxFileSize) {
                    isValid = false;
                    errorMessage = 'File too large (max 25MB)';
                } else if (!allowedTypes.includes(file.type)) {
                    isValid = false;
                    errorMessage = 'File type not allowed';
                }
                
                const fileInfo = document.createElement('div');
                fileInfo.className = 'flex items-center space-x-3';
                
                const fileIcon = getFileIcon(file.type);
                const fileSize = formatFileSize(file.size);
                
                fileInfo.innerHTML = `
                    <div class="w-8 h-8 flex items-center justify-center rounded-lg ${isValid ? 'bg-blue-100' : 'bg-red-100'}">
                        ${fileIcon}
                    </div>
                    <div>
                        <p class="text-sm font-medium ${isValid ? 'text-gray-900' : 'text-red-900'}">${file.name}</p>
                        <p class="text-xs ${isValid ? 'text-gray-500' : 'text-red-500'}">
                            ${isValid ? fileSize : errorMessage}
                        </p>
                    </div>
                `;
                
                const removeButton = document.createElement('button');
                removeButton.type = 'button';
                removeButton.className = 'text-gray-400 hover:text-red-500 transition-colors duration-200';
                removeButton.innerHTML = `
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                `;
                
                removeButton.addEventListener('click', function() {
                    fileItem.remove();
                    if (fileList.children.length === 0) {
                        filePreview.classList.add('hidden');
                    }
                });
                
                fileItem.appendChild(fileInfo);
                fileItem.appendChild(removeButton);
                fileList.appendChild(fileItem);
            });
            
            filePreview.classList.remove('hidden');
        });
    }

    function getFileIcon(mimeType) {
        if (mimeType.startsWith('image/')) {
            return '<svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path></svg>';
        } else if (mimeType === 'application/pdf') {
            return '<svg class="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd"></path></svg>';
        } else {
            return '<svg class="w-4 h-4 text-gray-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd"></path></svg>';
        }
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
});
</script>
@endsection
