<?php

use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\RegisterController;
use App\Http\Controllers\Auth\ForgotPasswordController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\ShopController;
use App\Http\Controllers\ProjectApplicationController;
use App\Http\Controllers\CareerController;

use App\Http\Controllers\CartController;
use App\Http\Controllers\CheckoutController;
use App\Http\Controllers\WebhookController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Home page
Route::get('/', function () {
    return view('pages.home');
})->name('home');

// Static pages
Route::get('/about', function () {
    return view('pages.about');
})->name('about');

Route::get('/contact', function () {
    return view('pages.contact');
})->name('contact');

Route::post('/contact', [HomeController::class, 'contactSubmit'])->name('contact.submit');

// Public Application Routes (No Authentication Required) - Using ProjectApplication system
Route::prefix('apply')->name('project-applications.')->group(function () {
    Route::get('/', [ProjectApplicationController::class, 'create'])->name('create');
    Route::post('/', [ProjectApplicationController::class, 'store'])->name('store');
    Route::get('/success/{referenceNumber}', [ProjectApplicationController::class, 'success'])->name('success');
    Route::post('/status', [ProjectApplicationController::class, 'status'])->name('status');
});

// Newsletter subscription
Route::post('/newsletter/subscribe', function () {
    // TODO: Implement newsletter subscription logic
    return redirect()->back()->with('success', 'Thank you for subscribing to our newsletter!');
})->name('newsletter.subscribe');

// Career Routes (Public Access)
Route::prefix('careers')->name('careers.')->group(function () {
    Route::get('/', [CareerController::class, 'index'])->name('index');
    Route::get('/job/{job}', [CareerController::class, 'show'])->name('show');
    Route::get('/job/{job}/apply', [CareerController::class, 'apply'])->name('apply');
    Route::post('/job/{job}/apply', [CareerController::class, 'storeApplication'])->name('store-application');
    Route::get('/application-success/{referenceNumber}', [CareerController::class, 'applicationSuccess'])->name('application-success');
    Route::post('/check-status', [CareerController::class, 'checkStatus'])->name('check-status');
});

// Shop Routes
Route::prefix('shop')->name('shop.')->group(function () {
    Route::get('/', [ShopController::class, 'index'])->name('index');
    Route::get('/search', [ShopController::class, 'search'])->name('search');
    Route::get('/category/{category}', [ShopController::class, 'category'])->name('category');
    Route::get('/product/{product}', [ShopController::class, 'product'])->name('product');
    Route::get('/product/{product}/quick-view', [ShopController::class, 'quickView'])->name('product.quick-view');
});

// Cart Routes
Route::prefix('cart')->name('cart.')->group(function () {
    Route::get('/', [CartController::class, 'index'])->name('index');
    Route::post('/add', [CartController::class, 'add'])->name('add');
    Route::patch('/update/{item}', [CartController::class, 'update'])->name('update');
    Route::delete('/remove/{item}', [CartController::class, 'remove'])->name('remove');
    Route::delete('/clear', [CartController::class, 'clear'])->name('clear');
    Route::get('/count', [CartController::class, 'count'])->name('count');
    Route::post('/coupon', [CartController::class, 'applyCoupon'])->name('coupon');
});

// Checkout Routes
Route::prefix('checkout')->name('checkout.')->group(function () {
    Route::get('/', [CheckoutController::class, 'index'])->name('index');
    Route::post('/process', [CheckoutController::class, 'process'])->name('process');
    Route::get('/payment/{order}', [CheckoutController::class, 'payment'])->name('payment');
    Route::post('/payment/{order}/complete', [CheckoutController::class, 'paymentComplete'])->name('payment.complete');
    Route::get('/success/{order}', [CheckoutController::class, 'success'])->name('success');
});

// Services routes
Route::prefix('services')->name('services.')->group(function () {
    Route::get('/', function () {
        return view('pages.services.index');
    })->name('index');

    Route::get('/web-development', function () {
        return view('pages.services.web-development');
    })->name('web-development');

    Route::get('/mobile-app-development', function () {
        return view('pages.services.mobile-app-development');
    })->name('mobile-app-development');

    Route::get('/ecommerce-development', function () {
        return view('pages.services.ecommerce-development');
    })->name('ecommerce-development');

    Route::get('/digital-marketing', function () {
        return view('pages.services.digital-marketing');
    })->name('digital-marketing');

    Route::get('/seo-services', function () {
        return view('pages.services.seo-services');
    })->name('seo-services');

    Route::get('/maintenance-support', function () {
        return view('pages.services.maintenance-support');
    })->name('maintenance-support');

    Route::get('/data-analytics', function () {
        return view('pages.services.data-analytics');
    })->name('data-analytics');
});

// Projects routes
Route::prefix('projects')->name('projects.')->group(function () {
    Route::get('/', function () {
        return view('pages.projects.index');
    })->name('index');
});

// Blog routes
Route::prefix('blog')->name('blog.')->group(function () {
    Route::get('/', function () {
        return view('pages.blog.index');
    })->name('index');
});



// Authentication routes
Route::get('/login', [LoginController::class, 'showLoginForm'])->name('login');
Route::post('/login', [LoginController::class, 'login']);
Route::get('/register', [RegisterController::class, 'showRegistrationForm'])->name('register');
Route::post('/register', [RegisterController::class, 'register']);
Route::post('/logout', [LoginController::class, 'logout'])->name('logout');

// Password Reset routes
Route::get('/forgot-password', [ForgotPasswordController::class, 'showLinkRequestForm'])->name('password.request');
Route::post('/forgot-password', [ForgotPasswordController::class, 'sendResetLinkEmail'])->name('password.email');
Route::get('/reset-password/{token}', [ForgotPasswordController::class, 'showResetForm'])->name('password.reset');
Route::post('/reset-password', [ForgotPasswordController::class, 'reset'])->name('password.update');

// Legal pages
Route::get('/terms', function () {
    return view('legal.terms');
})->name('terms');

Route::get('/privacy', function () {
    return view('legal.privacy');
})->name('privacy');

// Email verification routes (placeholder)
Route::get('/email/verify/{id}/{hash}', function () {
    return redirect()->route('dashboard')->with('success', 'Email verified successfully!');
})->name('verification.verify')->middleware(['auth', 'signed']);

Route::post('/email/verification-notification', function () {
    return back()->with('message', 'Verification link sent!');
})->name('verification.send')->middleware('auth');



// Admin Dashboard routes (protected)
Route::prefix('admin')->name('admin.')->middleware(['auth', 'role:admin,staff'])->group(function () {
    Route::get('/', [App\Http\Controllers\Admin\DashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard', [App\Http\Controllers\Admin\DashboardController::class, 'index'])->name('dashboard.alt');

    // Categories Management
    Route::resource('categories', App\Http\Controllers\Admin\CategoryController::class);
    Route::post('/categories/reorder', [App\Http\Controllers\Admin\CategoryController::class, 'reorder'])->name('categories.reorder');
    Route::get('/categories-api', [App\Http\Controllers\Admin\CategoryController::class, 'getCategories'])->name('categories.api');
    Route::post('/categories/{category}/toggle-featured', [App\Http\Controllers\Admin\CategoryController::class, 'toggleFeatured'])->name('categories.toggle-featured');

    // Products Management
    Route::resource('products', App\Http\Controllers\Admin\ProductController::class);
    Route::post('/products/{product}/images', [App\Http\Controllers\Admin\ProductController::class, 'uploadImages'])->name('products.images.upload');
    Route::delete('/products/{product}/images/{image}', [App\Http\Controllers\Admin\ProductController::class, 'deleteImage'])->name('products.images.delete');
    Route::post('/products/{product}/toggle-featured', [App\Http\Controllers\Admin\ProductController::class, 'toggleFeatured'])->name('products.toggle-featured');

    // Coupons Management
    Route::resource('coupons', App\Http\Controllers\Admin\CouponController::class);
    Route::post('/coupons/{coupon}/toggle', [App\Http\Controllers\Admin\CouponController::class, 'toggle'])->name('coupons.toggle');

    // Orders Management
    Route::resource('orders', App\Http\Controllers\Admin\OrderController::class)->only(['index', 'show', 'update']);
    Route::patch('/orders/{order}/status', [App\Http\Controllers\Admin\OrderController::class, 'updateStatus'])->name('orders.status');

    // Users Management
    Route::resource('users', App\Http\Controllers\Admin\UserController::class);
    Route::post('users/{user}/toggle', [App\Http\Controllers\Admin\UserController::class, 'toggle'])->name('users.toggle');
    Route::post('users/{user}/verify-email', [App\Http\Controllers\Admin\UserController::class, 'verifyEmail'])->name('users.verify-email');
    Route::post('users/{user}/resend-verification', [App\Http\Controllers\Admin\UserController::class, 'resendVerification'])->name('users.resend-verification');

    // Project Applications Management
    Route::resource('project-applications', App\Http\Controllers\Admin\ProjectApplicationController::class);
    Route::patch('/project-applications/{projectApplication}/status', [App\Http\Controllers\Admin\ProjectApplicationController::class, 'updateStatus'])->name('project-applications.update-status');
    Route::get('/project-applications/{projectApplication}/download/{attachmentIndex}', [App\Http\Controllers\Admin\ProjectApplicationController::class, 'downloadAttachment'])->name('project-applications.download');
    Route::get('/project-applications-statistics', [App\Http\Controllers\Admin\ProjectApplicationController::class, 'getStatistics'])->name('project-applications.statistics');

    // Jobs Management
    Route::resource('jobs', App\Http\Controllers\Admin\JobController::class);
    Route::post('/jobs/{job}/toggle-featured', [App\Http\Controllers\Admin\JobController::class, 'toggleFeatured'])->name('jobs.toggle-featured');
    Route::post('/jobs/{job}/toggle-active', [App\Http\Controllers\Admin\JobController::class, 'toggleActive'])->name('jobs.toggle-active');

    // Job Applications Management
    Route::resource('job-applications', App\Http\Controllers\Admin\JobApplicationController::class)->only(['index', 'show', 'update', 'destroy']);
    Route::patch('/job-applications/{jobApplication}/status', [App\Http\Controllers\Admin\JobApplicationController::class, 'updateStatus'])->name('job-applications.update-status');
    Route::get('/job-applications/{jobApplication}/download/{attachmentIndex}', [App\Http\Controllers\Admin\JobApplicationController::class, 'downloadAttachment'])->name('job-applications.download');
    Route::get('/job-applications-statistics', [App\Http\Controllers\Admin\JobApplicationController::class, 'getStatistics'])->name('job-applications.statistics');



    // Activity Logs - Admin and Staff only
    Route::get('/activity-logs', [App\Http\Controllers\Admin\ActivityLogController::class, 'index'])->name('activity-logs.index');
    Route::get('/activity-logs/{activityLog}', [App\Http\Controllers\Admin\ActivityLogController::class, 'show'])->name('activity-logs.show');
    Route::delete('/activity-logs/{activityLog}', [App\Http\Controllers\Admin\ActivityLogController::class, 'destroy'])->name('activity-logs.destroy');
    Route::get('/activity-logs-data', [App\Http\Controllers\Admin\ActivityLogController::class, 'data'])->name('activity-logs.data');
    Route::delete('/activity-logs/bulk-delete', [App\Http\Controllers\Admin\ActivityLogController::class, 'bulkDelete'])->name('activity-logs.bulk-delete');
    Route::delete('/activity-logs/clean-old', [App\Http\Controllers\Admin\ActivityLogController::class, 'cleanOldLogs'])->name('activity-logs.clean-old');
    Route::get('/activity-logs/export', [App\Http\Controllers\Admin\ActivityLogController::class, 'export'])->name('activity-logs.export');
    Route::get('/activity-logs-stats', [App\Http\Controllers\Admin\ActivityLogController::class, 'stats'])->name('activity-logs.stats');
});

// Customer Dashboard routes (protected with activity logging)
Route::middleware(['auth', 'activity.log'])->group(function () {
    Route::get('/dashboard', [App\Http\Controllers\DashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard/search', [App\Http\Controllers\DashboardController::class, 'search'])->name('dashboard.search');

    // Orders
    Route::get('/orders', [App\Http\Controllers\OrderController::class, 'index'])->name('orders.index');
    Route::get('/orders/{order}', [App\Http\Controllers\OrderController::class, 'show'])->name('orders.show');
    Route::delete('/orders/{order}', [App\Http\Controllers\OrderController::class, 'destroy'])->name('orders.destroy');

    // Projects (for clients)
    Route::get('/projects', [App\Http\Controllers\ProjectController::class, 'index'])->name('projects.index');
    Route::get('/projects/{project}', [App\Http\Controllers\ProjectController::class, 'show'])->name('projects.show');
    Route::delete('/projects/{project}', [App\Http\Controllers\ProjectController::class, 'destroy'])->name('projects.destroy');

    // Project Applications (Unified system for both public and authenticated users)
    Route::resource('project-applications', ProjectApplicationController::class);

    // Career Applications (for authenticated users)
    Route::get('/my-job-applications', [CareerController::class, 'myApplications'])->name('careers.my-applications');

    // Address Management
    Route::get('/addresses', [App\Http\Controllers\AddressController::class, 'index'])->name('addresses.index');
    Route::get('/addresses/create', [App\Http\Controllers\AddressController::class, 'create'])->name('addresses.create');
    Route::post('/addresses', [App\Http\Controllers\AddressController::class, 'store'])->name('addresses.store');
    Route::get('/addresses/{address}/edit', [App\Http\Controllers\AddressController::class, 'edit'])->name('addresses.edit');
    Route::patch('/addresses/{address}', [App\Http\Controllers\AddressController::class, 'update'])->name('addresses.update');
    Route::delete('/addresses/{address}', [App\Http\Controllers\AddressController::class, 'destroy'])->name('addresses.destroy');
    Route::patch('/addresses/{address}/set-default', [App\Http\Controllers\AddressController::class, 'setDefault'])->name('addresses.set-default');

    // Profile Management
    Route::get('/profile/edit', [App\Http\Controllers\ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [App\Http\Controllers\ProfileController::class, 'update'])->name('profile.update');
    Route::patch('/profile/password', [App\Http\Controllers\ProfileController::class, 'updatePassword'])->name('profile.password.update');
    Route::delete('/profile/avatar', [App\Http\Controllers\ProfileController::class, 'deleteAvatar'])->name('profile.avatar.delete');
    Route::get('/profile/delete-account', [App\Http\Controllers\ProfileController::class, 'deleteAccount'])->name('profile.delete-account');
    Route::delete('/profile', [App\Http\Controllers\ProfileController::class, 'destroyAccount'])->name('profile.destroy');

    
    // Client-specific routes can be added here
});

// Webhook routes (exclude from CSRF protection)
Route::post('/webhooks/stripe', [WebhookController::class, 'stripe'])->name('webhooks.stripe');
Route::post('/webhooks/paypal', [WebhookController::class, 'paypal'])->name('webhooks.paypal');
