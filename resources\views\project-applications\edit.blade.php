@extends('layouts.app')

@section('title', 'Edit Project Application')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Edit Project Application</h4>
                    <a href="{{ route('project-applications.show', $projectApplication) }}" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left"></i> Back to Details
                    </a>
                </div>
                <div class="card-body">
                    @if($projectApplication->status !== 'pending')
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            This application has been {{ $projectApplication->status }} and cannot be edited.
                        </div>
                    @else
                        <form action="{{ route('project-applications.update', $projectApplication) }}" 
                              method="POST" 
                              enctype="multipart/form-data" 
                              id="projectApplicationForm">
                            @csrf
                            @method('PUT')

                            <div class="row">
                                <div class="col-md-8">
                                    <div class="form-group mb-3">
                                        <label for="title" class="form-label">Project Title <span class="text-danger">*</span></label>
                                        <input type="text" 
                                               class="form-control @error('title') is-invalid @enderror" 
                                               id="title" 
                                               name="title" 
                                               value="{{ old('title', $projectApplication->title) }}" 
                                               required>
                                        @error('title')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="form-group mb-3">
                                        <label for="description" class="form-label">Project Description <span class="text-danger">*</span></label>
                                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                                  id="description" 
                                                  name="description" 
                                                  rows="4" 
                                                  required>{{ old('description', $projectApplication->description) }}</textarea>
                                        @error('description')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="form-group mb-3">
                                        <label for="requirements" class="form-label">Requirements</label>
                                        <textarea class="form-control @error('requirements') is-invalid @enderror" 
                                                  id="requirements" 
                                                  name="requirements" 
                                                  rows="3">{{ old('requirements', $projectApplication->requirements) }}</textarea>
                                        @error('requirements')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group mb-3">
                                                <label for="budget_range" class="form-label">Budget Range</label>
                                                <select class="form-control @error('budget_range') is-invalid @enderror" 
                                                        id="budget_range" 
                                                        name="budget_range">
                                                    <option value="">Select budget range</option>
                                                    <option value="1000-5000" {{ old('budget_range', $projectApplication->budget_range) === '1000-5000' ? 'selected' : '' }}>$1,000 - $5,000</option>
                                                    <option value="5000-10000" {{ old('budget_range', $projectApplication->budget_range) === '5000-10000' ? 'selected' : '' }}>$5,000 - $10,000</option>
                                                    <option value="10000-25000" {{ old('budget_range', $projectApplication->budget_range) === '10000-25000' ? 'selected' : '' }}>$10,000 - $25,000</option>
                                                    <option value="25000+" {{ old('budget_range', $projectApplication->budget_range) === '25000+' ? 'selected' : '' }}>$25,000+</option>
                                                </select>
                                                @error('budget_range')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group mb-3">
                                                <label for="timeline" class="form-label">Timeline</label>
                                                <select class="form-control @error('timeline') is-invalid @enderror" 
                                                        id="timeline" 
                                                        name="timeline">
                                                    <option value="">Select timeline</option>
                                                    <option value="1-2 weeks" {{ old('timeline', $projectApplication->timeline) === '1-2 weeks' ? 'selected' : '' }}>1-2 weeks</option>
                                                    <option value="2-4 weeks" {{ old('timeline', $projectApplication->timeline) === '2-4 weeks' ? 'selected' : '' }}>2-4 weeks</option>
                                                    <option value="1-2 months" {{ old('timeline', $projectApplication->timeline) === '1-2 months' ? 'selected' : '' }}>1-2 months</option>
                                                    <option value="2-3 months" {{ old('timeline', $projectApplication->timeline) === '2-3 months' ? 'selected' : '' }}>2-3 months</option>
                                                    <option value="3+ months" {{ old('timeline', $projectApplication->timeline) === '3+ months' ? 'selected' : '' }}>3+ months</option>
                                                </select>
                                                @error('timeline')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group mb-3">
                                                <label for="priority" class="form-label">Priority</label>
                                                <select class="form-control @error('priority') is-invalid @enderror" 
                                                        id="priority" 
                                                        name="priority">
                                                    <option value="low" {{ old('priority', $projectApplication->priority) === 'low' ? 'selected' : '' }}>Low</option>
                                                    <option value="medium" {{ old('priority', $projectApplication->priority) === 'medium' ? 'selected' : '' }}>Medium</option>
                                                    <option value="high" {{ old('priority', $projectApplication->priority) === 'high' ? 'selected' : '' }}>High</option>
                                                    <option value="urgent" {{ old('priority', $projectApplication->priority) === 'urgent' ? 'selected' : '' }}>Urgent</option>
                                                </select>
                                                @error('priority')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group mb-3">
                                                <label for="service_id" class="form-label">Service</label>
                                                <select class="form-control @error('service_id') is-invalid @enderror" 
                                                        id="service_id" 
                                                        name="service_id">
                                                    <option value="">Select a service</option>
                                                    @foreach($services as $service)
                                                        <option value="{{ $service->id }}" {{ old('service_id', $projectApplication->service_id) == $service->id ? 'selected' : '' }}>
                                                            {{ $service->name }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                                @error('service_id')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>

                                    <!-- File Upload Section -->
                                    <div class="form-group mb-3">
                                        <label for="attachments" class="form-label">Additional Files</label>
                                        <div class="file-upload-container">
                                            <div class="file-drop-zone" id="fileDropZone">
                                                <div class="file-drop-content">
                                                    <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                                    <p class="mb-2">Drag and drop files here or click to browse</p>
                                                    <p class="text-muted small">Maximum 15 files, 25MB each. Supported: PDF, DOC, DOCX, XLS, XLSX, JPG, PNG</p>
                                                </div>
                                                <input type="file" 
                                                       class="file-input @error('attachments.*') is-invalid @enderror" 
                                                       id="attachments" 
                                                       name="attachments[]" 
                                                       multiple 
                                                       accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png">
                                            </div>
                                            <div id="fileList" class="file-list mt-3"></div>
                                        </div>
                                        @error('attachments.*')
                                            <div class="invalid-feedback d-block">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    @if($projectApplication->attachments && count($projectApplication->attachments) > 0)
                                        <div class="mb-3">
                                            <h6>Current Attachments</h6>
                                            <div class="list-group">
                                                @foreach($projectApplication->attachments as $attachment)
                                                    <div class="list-group-item d-flex justify-content-between align-items-center">
                                                        <div>
                                                            <i class="fas fa-file"></i>
                                                            <strong>{{ $attachment['original_name'] }}</strong>
                                                            <small class="text-muted">({{ number_format($attachment['size'] / 1024, 1) }} KB)</small>
                                                        </div>
                                                        <a href="{{ Storage::url($attachment['stored_name']) }}" 
                                                           class="btn btn-sm btn-outline-primary" 
                                                           target="_blank">
                                                            <i class="fas fa-download"></i>
                                                        </a>
                                                    </div>
                                                @endforeach
                                            </div>
                                            <small class="text-muted">Note: New files will be added to existing attachments.</small>
                                        </div>
                                    @endif

                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save"></i> Update Application
                                        </button>
                                        <a href="{{ route('project-applications.show', $projectApplication) }}" class="btn btn-secondary">
                                            Cancel
                                        </a>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">Current Status</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <strong>Status:</strong>
                                                <span class="badge badge-{{ $projectApplication->status_color }}">
                                                    {{ ucfirst($projectApplication->status) }}
                                                </span>
                                            </div>
                                            <div class="mb-3">
                                                <strong>Submitted:</strong><br>
                                                {{ $projectApplication->created_at->format('M d, Y \a\t g:i A') }}
                                            </div>
                                            @if($projectApplication->reviewed_at)
                                                <div class="mb-3">
                                                    <strong>Last Reviewed:</strong><br>
                                                    {{ $projectApplication->reviewed_at->format('M d, Y \a\t g:i A') }}
                                                </div>
                                            @endif
                                        </div>
                                    </div>

                                    <div class="alert alert-info mt-3">
                                        <i class="fas fa-info-circle"></i>
                                        <strong>Note:</strong> You can only edit applications with "Pending" status.
                                    </div>
                                </div>
                            </div>
                        </form>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// File upload functionality (same as create form)
document.addEventListener('DOMContentLoaded', function() {
    const fileDropZone = document.getElementById('fileDropZone');
    const fileInput = document.getElementById('attachments');
    const fileList = document.getElementById('fileList');
    let selectedFiles = [];

    // Drag and drop functionality
    fileDropZone.addEventListener('dragover', function(e) {
        e.preventDefault();
        fileDropZone.classList.add('dragover');
    });

    fileDropZone.addEventListener('dragleave', function(e) {
        e.preventDefault();
        fileDropZone.classList.remove('dragover');
    });

    fileDropZone.addEventListener('drop', function(e) {
        e.preventDefault();
        fileDropZone.classList.remove('dragover');
        handleFiles(e.dataTransfer.files);
    });

    fileDropZone.addEventListener('click', function() {
        fileInput.click();
    });

    fileInput.addEventListener('change', function() {
        handleFiles(this.files);
    });

    function handleFiles(files) {
        for (let file of files) {
            if (selectedFiles.length >= 15) {
                alert('Maximum 15 files allowed');
                break;
            }
            if (file.size > 25 * 1024 * 1024) {
                alert(`File ${file.name} is too large. Maximum size is 25MB.`);
                continue;
            }
            selectedFiles.push(file);
        }
        updateFileList();
        updateFileInput();
    }

    function updateFileList() {
        fileList.innerHTML = '';
        selectedFiles.forEach((file, index) => {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item d-flex justify-content-between align-items-center p-2 border rounded mb-2';
            fileItem.innerHTML = `
                <div>
                    <i class="fas fa-file"></i>
                    <span>${file.name}</span>
                    <small class="text-muted">(${(file.size / 1024).toFixed(1)} KB)</small>
                </div>
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFile(${index})">
                    <i class="fas fa-times"></i>
                </button>
            `;
            fileList.appendChild(fileItem);
        });
    }

    function updateFileInput() {
        const dt = new DataTransfer();
        selectedFiles.forEach(file => dt.items.add(file));
        fileInput.files = dt.files;
    }

    window.removeFile = function(index) {
        selectedFiles.splice(index, 1);
        updateFileList();
        updateFileInput();
    };
});
</script>
@endpush

@push('styles')
<style>
.file-upload-container {
    position: relative;
}

.file-drop-zone {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: #fafafa;
}

.file-drop-zone:hover,
.file-drop-zone.dragover {
    border-color: #007bff;
    background-color: #f0f8ff;
}

.file-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.file-item {
    background-color: #f8f9fa;
}
</style>
@endpush
@endsection
