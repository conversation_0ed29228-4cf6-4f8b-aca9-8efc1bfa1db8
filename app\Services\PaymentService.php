<?php

namespace App\Services;

use App\Models\Order;
use App\Models\Payment;
use Stripe\Stripe;
use Stripe\Charge;
use Stripe\PaymentIntent;
use Stripe\Exception\CardException;
use Stripe\Exception\ApiErrorException;
use Illuminate\Support\Facades\Log;

class PaymentService
{
    public function __construct()
    {
        Stripe::setApiKey(config('services.stripe.secret'));
    }

    /**
     * Process a Stripe payment
     */
    public function processStripePayment(Order $order, string $token, array $metadata = []): array
    {
        try {
            // Create the charge
            $charge = Charge::create([
                'amount' => $this->convertToStripeAmount($order->total),
                'currency' => strtolower($order->currency),
                'source' => $token,
                'description' => "Order #{$order->order_number}",
                'metadata' => array_merge([
                    'order_id' => $order->id,
                    'order_number' => $order->order_number,
                    'customer_email' => $order->email,
                ], $metadata),
            ]);

            // Create payment record
            $payment = Payment::create([
                'order_id' => $order->id,
                'payment_method' => 'stripe',
                'payment_gateway' => 'stripe',
                'transaction_id' => $charge->id,
                'amount' => $order->total,
                'currency' => $order->currency,
                'status' => 'completed',
                'gateway_response' => json_encode($charge->toArray()),
                'processed_at' => now(),
            ]);

            // Update order status
            $order->update([
                'payment_status' => 'paid',
                'status' => 'processing',
            ]);

            return [
                'success' => true,
                'payment' => $payment,
                'charge' => $charge,
                'message' => 'Payment processed successfully',
            ];

        } catch (CardException $e) {
            // Card was declined
            Log::error('Stripe Card Error', [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
                'code' => $e->getStripeCode(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'type' => 'card_error',
            ];

        } catch (ApiErrorException $e) {
            // Stripe API error
            Log::error('Stripe API Error', [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
                'type' => $e->getStripeCode(),
            ]);

            return [
                'success' => false,
                'error' => 'Payment processing failed. Please try again.',
                'type' => 'api_error',
            ];

        } catch (\Exception $e) {
            // General error
            Log::error('Payment Processing Error', [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => 'An unexpected error occurred. Please try again.',
                'type' => 'general_error',
            ];
        }
    }

    /**
     * Create a PaymentIntent for Stripe (for SCA compliance)
     */
    public function createPaymentIntent(Order $order, array $metadata = []): array
    {
        try {
            $paymentIntent = PaymentIntent::create([
                'amount' => $this->convertToStripeAmount($order->total),
                'currency' => strtolower($order->currency),
                'description' => "Order #{$order->order_number}",
                'metadata' => array_merge([
                    'order_id' => $order->id,
                    'order_number' => $order->order_number,
                    'customer_email' => $order->email,
                ], $metadata),
                'automatic_payment_methods' => [
                    'enabled' => true,
                ],
            ]);

            return [
                'success' => true,
                'payment_intent' => $paymentIntent,
                'client_secret' => $paymentIntent->client_secret,
            ];

        } catch (ApiErrorException $e) {
            Log::error('Stripe PaymentIntent Error', [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => 'Failed to initialize payment. Please try again.',
            ];
        }
    }

    /**
     * Process PayPal payment (placeholder for future implementation)
     */
    public function processPayPalPayment(Order $order, array $paypalData): array
    {
        // TODO: Implement PayPal payment processing
        return [
            'success' => false,
            'error' => 'PayPal payment not yet implemented',
        ];
    }

    /**
     * Refund a payment
     */
    public function refundPayment(Payment $payment, float $amount = null): array
    {
        try {
            if ($payment->payment_gateway !== 'stripe') {
                return [
                    'success' => false,
                    'error' => 'Refunds only supported for Stripe payments',
                ];
            }

            $refundAmount = $amount ? $this->convertToStripeAmount($amount) : null;

            $refund = \Stripe\Refund::create([
                'charge' => $payment->transaction_id,
                'amount' => $refundAmount,
            ]);

            // Update payment record
            $payment->update([
                'status' => $refundAmount ? 'partially_refunded' : 'refunded',
                'refunded_amount' => ($payment->refunded_amount ?? 0) + ($amount ?? $payment->amount),
                'gateway_response' => json_encode(array_merge(
                    json_decode($payment->gateway_response, true) ?? [],
                    ['refund' => $refund->toArray()]
                )),
            ]);

            return [
                'success' => true,
                'refund' => $refund,
                'message' => 'Refund processed successfully',
            ];

        } catch (ApiErrorException $e) {
            Log::error('Stripe Refund Error', [
                'payment_id' => $payment->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => 'Refund processing failed. Please try again.',
            ];
        }
    }

    /**
     * Convert amount to Stripe format (cents)
     */
    private function convertToStripeAmount(float $amount): int
    {
        return (int) round($amount * 100);
    }

    /**
     * Convert amount from Stripe format (cents) to decimal
     */
    private function convertFromStripeAmount(int $amount): float
    {
        return $amount / 100;
    }

    /**
     * Verify webhook signature
     */
    public function verifyWebhookSignature(string $payload, string $signature): bool
    {
        try {
            \Stripe\Webhook::constructEvent(
                $payload,
                $signature,
                config('services.stripe.webhook_secret')
            );
            return true;
        } catch (\Exception $e) {
            Log::error('Stripe Webhook Signature Verification Failed', [
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Handle webhook events
     */
    public function handleWebhookEvent(array $event): void
    {
        switch ($event['type']) {
            case 'payment_intent.succeeded':
                $this->handlePaymentIntentSucceeded($event['data']['object']);
                break;

            case 'payment_intent.payment_failed':
                $this->handlePaymentIntentFailed($event['data']['object']);
                break;

            case 'charge.dispute.created':
                $this->handleChargeDispute($event['data']['object']);
                break;

            default:
                Log::info('Unhandled Stripe webhook event', ['type' => $event['type']]);
        }
    }

    /**
     * Handle successful payment intent
     */
    private function handlePaymentIntentSucceeded(array $paymentIntent): void
    {
        $orderId = $paymentIntent['metadata']['order_id'] ?? null;
        
        if ($orderId) {
            $order = Order::find($orderId);
            if ($order && $order->payment_status !== 'paid') {
                $order->update([
                    'payment_status' => 'paid',
                    'status' => 'processing',
                ]);

                // Create payment record if it doesn't exist
                Payment::firstOrCreate(
                    ['transaction_id' => $paymentIntent['id']],
                    [
                        'order_id' => $order->id,
                        'payment_method' => 'stripe',
                        'payment_gateway' => 'stripe',
                        'amount' => $this->convertFromStripeAmount($paymentIntent['amount']),
                        'currency' => strtoupper($paymentIntent['currency']),
                        'status' => 'completed',
                        'gateway_response' => json_encode($paymentIntent),
                        'processed_at' => now(),
                    ]
                );
            }
        }
    }

    /**
     * Handle failed payment intent
     */
    private function handlePaymentIntentFailed(array $paymentIntent): void
    {
        $orderId = $paymentIntent['metadata']['order_id'] ?? null;
        
        if ($orderId) {
            $order = Order::find($orderId);
            if ($order) {
                $order->update([
                    'payment_status' => 'failed',
                    'status' => 'cancelled',
                ]);

                // Create failed payment record
                Payment::create([
                    'order_id' => $order->id,
                    'payment_method' => 'stripe',
                    'payment_gateway' => 'stripe',
                    'transaction_id' => $paymentIntent['id'],
                    'amount' => $this->convertFromStripeAmount($paymentIntent['amount']),
                    'currency' => strtoupper($paymentIntent['currency']),
                    'status' => 'failed',
                    'gateway_response' => json_encode($paymentIntent),
                    'processed_at' => now(),
                ]);
            }
        }
    }

    /**
     * Handle charge dispute
     */
    private function handleChargeDispute(array $dispute): void
    {
        $chargeId = $dispute['charge'];
        
        $payment = Payment::where('transaction_id', $chargeId)->first();
        if ($payment) {
            $payment->update([
                'status' => 'disputed',
                'gateway_response' => json_encode(array_merge(
                    json_decode($payment->gateway_response, true) ?? [],
                    ['dispute' => $dispute]
                )),
            ]);

            // Update order status
            $payment->order->update([
                'payment_status' => 'disputed',
            ]);
        }
    }
}
