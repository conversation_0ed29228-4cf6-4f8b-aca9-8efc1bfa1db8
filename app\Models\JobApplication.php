<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class JobApplication extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'uuid',
        'reference_number',
        'job_id',
        'user_id',
        // Personal Information
        'first_name',
        'last_name',
        'email',
        'phone',
        'date_of_birth',
        'nationality',
        'id_number',
        // Contact Information
        'address',
        'city',
        'province',
        'postal_code',
        'country',
        // Professional Information
        'cover_letter',
        'experience_summary',
        'current_position',
        'current_company',
        'current_salary',
        'expected_salary',
        'notice_period',
        'available_start_date',
        // Education
        'highest_qualification',
        'institution',
        'field_of_study',
        'graduation_year',
        // Skills and Preferences
        'skills',
        'languages',
        'willing_to_relocate',
        'willing_to_travel',
        'has_drivers_license',
        'has_own_transport',
        // Application Status
        'status',
        'admin_notes',
        'reviewed_by',
        'reviewed_at',
        // File Attachments
        'attachments',
        // Additional Information
        'additional_notes',
        'newsletter_signup',
        'terms_accepted',
        // Tracking
        'ip_address',
        'user_agent',
        'referrer',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'date_of_birth' => 'date',
        'current_salary' => 'decimal:2',
        'expected_salary' => 'decimal:2',
        'available_start_date' => 'date',
        'graduation_year' => 'integer',
        'skills' => 'array',
        'languages' => 'array',
        'willing_to_relocate' => 'boolean',
        'willing_to_travel' => 'boolean',
        'has_drivers_license' => 'boolean',
        'has_own_transport' => 'boolean',
        'attachments' => 'array',
        'newsletter_signup' => 'boolean',
        'terms_accepted' => 'boolean',
        'reviewed_at' => 'datetime',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($application) {
            if (empty($application->uuid)) {
                $application->uuid = (string) Str::uuid();
            }
            if (empty($application->reference_number)) {
                $application->reference_number = 'JOB-' . strtoupper(Str::random(8));
            }
        });
    }

    /**
     * Get the job that this application belongs to.
     */
    public function job(): BelongsTo
    {
        return $this->belongsTo(Job::class);
    }

    /**
     * Get the user that submitted this application.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the user who reviewed this application.
     */
    public function reviewedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewed_by');
    }

    /**
     * Get status options.
     */
    public static function getStatusOptions(): array
    {
        return [
            'pending' => 'Pending Review',
            'reviewing' => 'Under Review',
            'shortlisted' => 'Shortlisted',
            'interviewed' => 'Interviewed',
            'offered' => 'Job Offered',
            'hired' => 'Hired',
            'rejected' => 'Rejected',
        ];
    }

    /**
     * Get status badge color.
     */
    public function getStatusBadgeColorAttribute(): string
    {
        return match($this->status) {
            'pending' => 'bg-yellow-100 text-yellow-800',
            'reviewing' => 'bg-blue-100 text-blue-800',
            'shortlisted' => 'bg-purple-100 text-purple-800',
            'interviewed' => 'bg-indigo-100 text-indigo-800',
            'offered' => 'bg-green-100 text-green-800',
            'hired' => 'bg-emerald-100 text-emerald-800',
            'rejected' => 'bg-red-100 text-red-800',
            default => 'bg-gray-100 text-gray-800',
        };
    }

    /**
     * Get full name attribute.
     */
    public function getFullNameAttribute(): string
    {
        return $this->first_name . ' ' . $this->last_name;
    }

    /**
     * Scope a query to only include applications with specific status.
     */
    public function scopeWithStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope a query to only include recent applications.
     */
    public function scopeRecent($query, $days = 30)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }
}
